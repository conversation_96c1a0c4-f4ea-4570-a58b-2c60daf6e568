package com.hailiang.composition.ui.photograph

import android.content.Context
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.hailiang.camera.pictureselector.entity.HLLocalMedia
import com.hailiang.camera.pictureselector.entity.HLSelectMimeType
import com.hailiang.common.base.BaseViewModel
import com.hailiang.common.util.AppToast
import com.hailiang.composition.data.Repository
import com.hailiang.composition.data.bean.CreateWorkBean
import com.hailiang.composition.data.bean.OcrSecondBean
import com.hailiang.composition.data.bean.TaskBean
import com.hailiang.composition.data.bean.TaskMaterialBean
import com.hailiang.composition.mediatools.MediaImage
import com.hailiang.core.ext.set
import com.hailiang.hlobs.FileServiceManager
import com.hailiang.hlobs.UploadCallback
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.launchCatch
import com.hailiang.libhttp.BaseRetrofit
import com.hailiang.ui.designsystem.toast.ToastUtils
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.suspendCancellableCoroutine
import java.io.File

class PhotoViewModel : BaseViewModel() {

    var isCorrectingOrSubmitting = false // 正在提交或校正中

    companion object {
        const val TYPE_TITLE = 0       // 题目
        const val TYPE_COMPOSITION = 1 // 作文
        const val TYPE_ALL = 3       // 题目
        const val TIME_FIRST = 1
        const val TIME_SECOND = 2
    }

    var outputDirectory: String = ""
    val maxTitleNum = 2
    val maxCompositionNum = 5

    // 已上传过，重新上传时用到
    var curWorkId: Long = 0
    var curWorkStateId: Long = 0
    var clearType = -1


    // 类型 0-题目 1-作文
    val typeLiveData = MutableLiveData<Int>()
    var timeLiveData = MutableLiveData<Int>()

    val titlePictureListLiveData = MutableLiveData<MutableList<MediaImage>>()
    val compositionPictureListLiveData = MutableLiveData<MutableList<MediaImage>>()

    val stateChangeLiveData = MutableLiveData<StateChangeEvent>()
    val addCompositionResultLiveData = MutableLiveData<Boolean>()
    val addCompositionSecondResultLiveData = MutableLiveData<Boolean>()


    private val repository = Repository()

    init {
        typeLiveData.value = TYPE_TITLE
        timeLiveData.value = TIME_FIRST
        titlePictureListLiveData.value = mutableListOf()
        compositionPictureListLiveData.value = mutableListOf()
        addCompositionResultLiveData.value = false
        addCompositionSecondResultLiveData.value = false
    }

    fun toCorrect(
        titlePhoto: ArrayList<MediaImage>, compositionPhoto: ArrayList<MediaImage>, context: Context
    ) {
        if (isCorrectingOrSubmitting) {
            return
        }
        isCorrectingOrSubmitting = true
        viewModelScope.launchCatch(
            errorBlock = {
                isCorrectingOrSubmitting = false
                AppToast.toast("图片上传失败", AppToast.LEVEL_FAIL)
            }) {
            try {
                val listT = mediaUploadOss(titlePhoto, context)
                val listC = mediaUploadOss(compositionPhoto, context)
                if (listT.contains(null) || listC.contains(null)) {
                    AppToast.toast("图片上传失败", AppToast.LEVEL_FAIL)
                    return@launchCatch
                }

                val createWorkBean = CreateWorkBean().apply {
                    schoolworkId = if (curWorkId > 0) curWorkId else null
                    schoolworkStateId = if (curWorkStateId > 0) curWorkStateId else null
                    compositionImageList = listC.filterNotNull()
                    taskList = listOf(
                        TaskBean(
                            id = -1, submitType = "takePhoto",
                            taskMaterialList = listT.filterNotNull().map { url ->
                                TaskMaterialBean(
                                    materialFileType = "picture", materialType = "task", materialFileUrl = url
                                )
                            })
                    )
                }
                val data = repository.addComposition(createWorkBean)
                if (data.isSuccess) {
                    data.data?.let {
                        curWorkId = it.schoolworkId
                        curWorkStateId = it.schoolworkStateId
                        addCompositionResultLiveData.postValue(true)
                    }
                } else {
                    AppToast.toast(data.message, AppToast.LEVEL_FAIL)
                }
            } finally {
                isCorrectingOrSubmitting = false
            }

        }
    }

    fun getCompositionDetail() {
        viewModelScope.launchCatch(errorBlock = { finish.set() }) {
            val workDetail = repository.requestCompositionInfo(
                curWorkId, curWorkStateId
            )
            if (workDetail == null) {
                ToastUtils.showShort("作业信息加载失败!")
                finish.set()
                return@launchCatch
            }
            if (timeLiveData.value == TIME_FIRST) {
                val title = if (clearType == TYPE_TITLE || clearType == TYPE_ALL) {
                    null
                } else {
                    workDetail.taskList?.firstOrNull {
                        it.submitType == "takePhoto"
                    }?.taskMaterialList?.map {
                        MediaImage(
                            MediaImage.STATE_SUCCESS, media = HLLocalMedia(
                                0, 0, 0, "", HLSelectMimeType.SYSTEM_IMAGE, it.materialFileUrl,
                            ), url = it.materialFileUrl
                        )
                    }
                }?.toMutableList()?: mutableListOf()

                val composition = if (clearType == TYPE_COMPOSITION || clearType ==TYPE_ALL) {
                    null
                } else {
                    workDetail.imgInfoList?.map {
                        MediaImage(
                            MediaImage.STATE_SUCCESS, media = HLLocalMedia(
                                0, 0, 0, "", HLSelectMimeType.SYSTEM_IMAGE, it.answerPicUrl,
                            ), url = it.answerPicUrl
                        )
                    }
                }?.toMutableList() ?: mutableListOf()

                compositionPictureListLiveData.postValue(composition)
                titlePictureListLiveData.postValue(title)

//                stateChangeLiveData.postValue(
//                    StateChangeEvent(
//                        TYPE_COMPOSITION, StateChangeEvent.CHANGE_TYPE_ADD,
//                    )
//                )

//                stateChangeLiveData.postValue(
//                    StateChangeEvent(
//                        TYPE_TITLE, StateChangeEvent.CHANGE_TYPE_ADD
//                    )
//                )
            }
        }
    }

    fun submit(
        compositionPhoto: ArrayList<MediaImage>, context: Context
    ) {
        if (isCorrectingOrSubmitting) {
            return
        }
        isCorrectingOrSubmitting = true
        viewModelScope.launchCatch(
            errorBlock = {
                isCorrectingOrSubmitting = false
                AppToast.toast("图片上传失败", AppToast.LEVEL_FAIL)
            }) {
            try {
                val listC = mediaUploadOss(compositionPhoto, context)
                if (listC.contains(null)) {
                    AppToast.toast("图片上传失败", AppToast.LEVEL_FAIL)
                    return@launchCatch
                }
                val data = repository.addOcrSecond(
                    workId = if (curWorkId > 0) curWorkId else null, ocrSecondBean = OcrSecondBean(
                        schoolworkStateId = if (curWorkStateId > 0) curWorkStateId else null,
                        imageUrlList = listC.mapNotNull { it })
                )
                if (data.isSuccess) {
                    data.data?.let {
                        addCompositionSecondResultLiveData.postValue(true)
                    }
                } else {
                    AppToast.toast(data.message, AppToast.LEVEL_FAIL)
                }
            } finally {
                isCorrectingOrSubmitting = false
            }
        }
    }
}

private suspend fun mediaUploadOss(
    mediaList: MutableList<MediaImage>, context: Context
): List<String?> {
    val imgUrlsDeferred = mediaList.map { media ->
        coroutineScope {
            async {
                uploadPicture(media, context)
            }
        }
    }
    val imgUrls = imgUrlsDeferred.map { it.await() }
    return imgUrls
}

suspend fun uploadPicture(
    mi: MediaImage,
    context: Context,
): String? = suspendCancellableCoroutine { continuation ->
    if (!mi.url.isNullOrEmpty()) {
        continuation.resume(mi.url) { /* 可选的取消处理 */ }
        return@suspendCancellableCoroutine
    }
    if (mi.media.url.isNotEmpty()) {
        continuation.resume(mi.media.url) { /* 可选的取消处理 */ }
        return@suspendCancellableCoroutine
    }

    val file = File(mi.media.path)
    FileServiceManager.uploadWithPrivate(
        context,
        BaseRetrofit.getInstance().baseUrl().toString(),
        file,
        FileServiceManager.createOssObjectName("workcloud", "${file.name}"),
        object : UploadCallback {
            override fun onFailure(e: Throwable?) {
                continuation.resume(null) { }
                AppToast.toast("图片上传失败", AppToast.LEVEL_FAIL)
            }

            override fun onProgress(currentSize: Long, totalSize: Long) {
            }

            override fun onSucceed(url: String) {
                continuation.resume(url) { }
            }
        })
}


data class StateChangeEvent(
    var type: Int, var changeType: Int,
) {
    companion object {
        const val CHANGE_TYPE_ADD = 0
        const val CHANGE_TYPE_DELETE = 1
    }
}